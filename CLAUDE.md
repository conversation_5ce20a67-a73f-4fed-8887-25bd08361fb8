# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DataHero4 is a monorepo-based conversational AI data analysis system that translates natural language questions into SQL queries, providing insights, visualizations, and follow-up suggestions. Built with FastAPI/LangGraph (backend) and React/TypeScript (frontend).

**Latest Update:** Complete dashboard system with dynamic KPI management - users can add/remove KPIs via visual selection modal with 34 available metrics, real-time calculation, and natural language editing capabilities.

## Development Notes

- Para rodar em desenvolvimento, use dev.sh
- Lembre-se de sempre, antes de planejar qualquer coisa ou fazer um bug fix, de pesquisar Exa Search, por exemplos modernos e comprovados de sucesso
- Não podemos criar soluções que dependam de alterações no banco de dados do cliente, onde há os dados a serem calculados
- Temos que lembrar que a aplicação é uma SaaS em cloud - portanto, não pode haver cache que desconsidere isso.
- Sempre termine os testes uando o MCP Playwright
- Não use fallbacks, mocks, ou soluções simplificadas - use a lógca de fail fast and loud

## Essential Commands

### Development
```bash
# Start both backend and frontend
npm run dev

# Backend only (port 8000)
npm run dev:backend

# Frontend only (port 3000)
npm run dev:frontend

# Backend CLI execution (from apps/backend/)
poetry run python src/main.py "question" --client-id L2M --sector cambio
```