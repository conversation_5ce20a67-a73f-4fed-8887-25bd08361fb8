import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Star,
  AlertCircle,
  Users,
  Trophy,
  Medal,
  Award
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import {
  BarChart, 
  Bar, 
  ResponsiveContainer, 
  XAxis, 
  YAxis, 
  Tooltip,
  CartesianGrid,
  Cell
} from 'recharts';
import { KpiData, DashboardFilters } from '@/types/dashboard';
import { cn } from '@/lib/utils';
import { kpiEvents } from '@/lib/events';
import { formatKpiValue, formatChangePercent, getTrendColorClass } from '@/lib/formatters';

interface KpiTopRankingCardProps {
  kpi: KpiData;
  filters: DashboardFilters;
  onRemoveKpi?: (kpiId: string) => void;
  isLarge?: boolean;
  isSelected?: boolean;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiTopRankingCard: React.FC<KpiTopRankingCardProps> = ({
  kpi,
  filters,
  onRemoveKpi,
  isLarge = false,
  isSelected = false,
  periodData
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const formatValue = (value: number) => {
    return formatKpiValue(value, kpi.format_type || kpi.format || 'number', filters);
  };

  // Get ranking data from KPI
  const rankingData = kpi.rankingData || [];
  const chartData = kpi.chartData?.data || kpi.chartData || [];



  // Use standard colors instead of special colors
  const barColors = ['#3B82F6', '#6366F1', '#8B5CF6']; // Blue shades

  // Function to get card styles based on state
  const getCardStyles = (isSelected: boolean, isHovered: boolean) => {
    const baseStyles = "h-full relative overflow-hidden transition-all duration-500 bg-white cursor-pointer";

    if (isSelected) {
      return cn(
        baseStyles,
        "border-2 border-blue-500 shadow-xl ring-2 ring-blue-100",
        "transform scale-[1.02]",
        "bg-blue-50/30"
      );
    }

    if (isHovered) {
      return cn(baseStyles, "hover:shadow-xl shadow-xl");
    }

    return cn(
      baseStyles,
      "hover:shadow-xl",
      kpi.alert ? "border-amber-400 border-2" : "border-gray-200",
      (kpi.is_priority || kpi.isPriority) && "ring-2 ring-amber-100"
    );
  };

  const getTrendIcon = () => {
    if (!kpi.changePercent) return null;

    const Icon = kpi.changePercent > 0 ? TrendingUp : TrendingDown;
    const color = getTrendColorClass(kpi.changePercent);

    return (
      <div className={cn("flex items-center gap-1", color)}>
        <Icon className="w-4 h-4" />
        <span className="text-sm font-medium">
          {formatChangePercent(kpi.changePercent)}
        </span>
      </div>
    );
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-4 h-4 text-yellow-500" />;
      case 2:
        return <Medal className="w-4 h-4 text-gray-400" />;
      case 3:
        return <Award className="w-4 h-4 text-amber-600" />;
      default:
        return <span className="w-4 h-4 flex items-center justify-center text-xs font-bold text-gray-500">#{rank}</span>;
    }
  };

  const renderChart = () => {
    if (!chartData || chartData.length === 0) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <Users className="w-8 h-8 mr-2" />
          <span>Sem dados de ranking</span>
        </div>
      );
    }



    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
        >
          {/* Removed: CartesianGrid, XAxis, YAxis for minimalist style */}
          <Tooltip
            formatter={(value: number, name: string, props: any) => [
              formatValue(value),
              props.payload?.name || 'Cliente'
            ]}
            labelFormatter={(label: string) => `${label}`}
            contentStyle={{
              backgroundColor: '#1f2937',
              border: '1px solid #374151',
              borderRadius: '8px',
              color: '#f9fafb',
              fontSize: '12px',
              padding: '8px 12px',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
            }}
            cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
          />
          <Bar
            dataKey="value"
            radius={[4, 4, 0, 0]}
            stroke="none"
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={barColors[index] || '#3B82F6'}
                opacity={0.8}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderRankingList = () => {
    if (!rankingData || rankingData.length === 0) {
      return (
        <div className="text-center text-gray-500 py-4">
          <Users className="w-6 h-6 mx-auto mb-2" />
          <p className="text-sm">Nenhum dado de ranking disponível</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {rankingData.slice(0, isLarge ? 10 : 5).map((client: any) => (
          <div key={client.cliente_id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getRankIcon(client.rank)}
              <div>
                <p className="text-sm font-medium text-gray-900 truncate max-w-[120px]">
                  {client.cliente_nome}
                </p>
                <p className="text-xs text-gray-500">
                  {client.total_operacoes} operações
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-semibold text-gray-900">
                {formatValue(client.volume_mn)}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const cardContent = (
    <div className={cn("p-4 h-full flex flex-col", isLarge ? "p-6" : "p-4")}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Trophy className="w-5 h-5 text-amber-500 flex-shrink-0" />
            <h3 className={cn(
              "font-semibold text-gray-900 truncate",
              isLarge ? "text-lg" : "text-sm"
            )}>
              {kpi.title || kpi.name}
            </h3>
          </div>
          {kpi.description && (
            <p className="text-xs text-gray-500 line-clamp-2">
              {kpi.description}
            </p>
          )}
        </div>
        
        {/* Priority Star */}
        {(kpi.is_priority || kpi.isPriority) && (
          <Star 
            className="w-5 h-5 text-amber-400 fill-current flex-shrink-0 ml-2"
            data-kpi-action="toggle-priority"
            data-kpi-id={kpi.id}
          />
        )}
        
        {/* Alert Icon */}
        {kpi.alert && (
          <AlertCircle className="w-5 h-5 text-amber-500 flex-shrink-0 ml-2" />
        )}
      </div>

      {/* Value Section */}
      <div className={cn(
        "flex items-center justify-between",
        isLarge ? "mb-4" : "mb-3"
      )}>
        <div 
          className={cn(
            "font-bold text-gray-900 truncate",
            isLarge ? "text-2xl" : "text-lg"
          )}
          data-testid={`${kpi.id}-value`}
        >
          {formatValue(kpi.currentValue)}
        </div>
        <div className="flex-shrink-0 ml-2">
          {getTrendIcon()}
        </div>
      </div>

      {/* Chart or List Section */}
      <div className={cn(
        "flex-1 min-h-0 overflow-hidden",
        isLarge ? "min-h-[200px]" : "min-h-[120px]"
      )}>
        {isLarge ? (
          <div className="h-full flex flex-col">
            <div className="h-1/2 mb-4">
              {renderChart()}
            </div>
            <div className="h-1/2 overflow-y-auto">
              {renderRankingList()}
            </div>
          </div>
        ) : (
          renderChart()
        )}
      </div>
    </div>
  );

  return (
    <motion.div
      className="h-full"
      whileHover={{ scale: 1.02 }}
      transition={{ 
        type: "spring", 
        stiffness: 300,
        damping: 30
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      layout
    >
      <Card
        className={getCardStyles(isSelected, isHovered)}
        data-testid="kpi-top-ranking-card"
        data-kpi-id={kpi.id}
        onClick={(e) => {
          const target = e.target as HTMLElement;
          const starElement = target.closest('[data-kpi-action="toggle-priority"]');
          
          if (starElement) {
            return;
          }
          
          const cardElement = e.currentTarget as HTMLElement;
          const rect = cardElement.getBoundingClientRect();
          kpiEvents.selectKPI({ 
            kpiId: kpi.id, 
            element: cardElement,
            cardRect: rect 
          });
        }}
      >
        {cardContent}
      </Card>
    </motion.div>
  );
};

export default KpiTopRankingCard;
