import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Trophy, Medal, Award, Users } from 'lucide-react';

interface ClientRankingData {
  rank: number;
  cliente_id: number;
  cliente_nome: string;
  volume_mn: number;
  volume_me: number;
  total_operacoes: number;
  ticket_medio_mn: number;
  primeira_operacao: string;
  ultima_operacao: string;
}

interface Props {
  kpiId: string;
  rankingData?: ClientRankingData[];
  isLoading?: boolean;
}

const KPIClientRankingTable: React.FC<Props> = ({
  kpiId,
  rankingData = [],
  isLoading = false
}) => {
  // Debug: Log component render
  console.log('🏆 [KPIClientRankingTable] Rendering:', {
    kpiId,
    rankingDataLength: rankingData?.length,
    isLoading,
    rankingData: rankingData?.slice(0, 2) // Show first 2 items
  });
  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `R$ ${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `R$ ${(value / 1000).toFixed(1)}K`;
    } else {
      return `R$ ${value.toFixed(2)}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return (
          <div className="w-5 h-5 flex items-center justify-center text-xs font-bold text-gray-500 bg-gray-100 rounded-full">
            {rank}
          </div>
        );
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 2:
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 3:
        return 'bg-amber-100 text-amber-800 border-amber-200';
      default:
        return 'bg-blue-50 text-blue-700 border-blue-200';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Ranking de Clientes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!rankingData || rankingData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Ranking de Clientes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-sm">Nenhum dado de ranking disponível</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          Ranking de Clientes - Top {rankingData.length}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-hidden rounded-lg border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Posição
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliente
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Volume (MN)
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Operações
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ticket Médio
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Período
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rankingData.map((client) => (
                  <tr 
                    key={client.cliente_id}
                    className={`hover:bg-gray-50 transition-colors ${
                      client.rank <= 3 ? 'bg-gradient-to-r from-yellow-50/30 to-transparent' : ''
                    }`}
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        {getRankIcon(client.rank)}
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRankBadgeColor(client.rank)}`}>
                          #{client.rank}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="text-sm font-medium text-gray-900 max-w-[200px] truncate" title={client.cliente_nome}>
                        {client.cliente_nome}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: {client.cliente_id}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-right">
                      <div className="text-sm font-semibold text-gray-900">
                        {formatValue(client.volume_mn)}
                      </div>
                      <div className="text-xs text-gray-500">
                        ME: {formatValue(client.volume_me)}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {client.total_operacoes}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-right">
                      <div className="text-sm text-gray-900">
                        {formatValue(client.ticket_medio_mn)}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div className="text-xs text-gray-500">
                        <div>{formatDate(client.primeira_operacao)}</div>
                        <div className="text-gray-400">até</div>
                        <div>{formatDate(client.ultima_operacao)}</div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        
        {rankingData.length > 0 && (
          <div className="mt-4 text-xs text-gray-500 text-center">
            Mostrando {rankingData.length} clientes ordenados por volume de transações
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default KPIClientRankingTable;
