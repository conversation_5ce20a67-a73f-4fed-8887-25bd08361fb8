import React, { useEffect, useState, useMemo } from 'react';
import { useKPIDrawer } from '@/hooks/useKPIDrawer';
import { KPIDrawerContent } from './KPIDrawerContent';
import { motion, AnimatePresence } from 'framer-motion';
import KpiBentoCard from '@/components/dashboard/KpiBentoCard';
import { useKpis } from '@/hooks/useKpis';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';
import { useKpiHistory } from '@/hooks/useKpiHistory';
import { kpiEvents } from '@/lib/events';
import { KpiData, ChartDataPoint } from '@/types/kpi';

interface SelectedCardData {
  kpiId: string;
}

interface KPIDrawerProps {
  kpisData?: KpiData[];
}

export const KPIDrawer: React.FC<KPIDrawerProps> = ({ kpisData }) => {
  const { isOpen, currentKPI, originalElement, closeDrawer } = useKPIDrawer();

  // Debug: Log props data
  console.log('🔍 [KPIDrawer] Props debug:', {
    kpisDataCount: kpisData?.length,
    kpisDataIds: kpisData?.map(k => k.id),
    currentKPI,
    isOpen
  });
  const [selectedCardData, setSelectedCardData] = useState<SelectedCardData | null>(null);

  // Get dashboard filters and KPIs
  const { filters: dashboardFilters } = useDashboardFilters();

  // TODO: Get selected profile from context or state management
  // For now, using CEO as default since that's what's being used in Dashboard
  const selectedProfile = 'CEO';

  // Local state for drawer filters (starts with dashboard filters)
  const [drawerFilters, setDrawerFilters] = useState(dashboardFilters);

  const { kpis, togglePriority } = useKpis({
    filters: drawerFilters,
    enablePersonalization: true,
    userId: 'test_user_ceo',
    profileType: selectedProfile
  });

  // Find the current KPI data - use props data if available, otherwise fetch
  const currentKpiData = kpisData?.find(kpi => kpi.id === currentKPI) ||
                        kpis.find(kpi => kpi.id === currentKPI);

  // Debug: Log current KPI data
  console.log('🔍 [KPIDrawer] Current KPI data:', {
    currentKPI,
    foundInProps: !!kpisData?.find(kpi => kpi.id === currentKPI),
    foundInFetch: !!kpis.find(kpi => kpi.id === currentKPI),
    hasRankingData: !!currentKpiData?.rankingData,
    rankingDataLength: currentKpiData?.rankingData?.length
  });

  // Fetch historical data for the current KPI
  const {
    historyData,
    isLoading: isHistoryLoading,
    error: historyError
  } = useKpiHistory({
    kpiId: currentKPI,
    timeframe: drawerFilters.timeframe,
    currency: drawerFilters.currency,
    enabled: isOpen && !!currentKPI
  });

  // Combine KPI data with historical chart data
  const kpiWithChartData = useMemo((): KpiData | null => {
    if (!currentKpiData) return null;

    // Convert history data to chart data format
    const chartData: ChartDataPoint[] = historyData?.history_data?.map(record => ({
      name: record.period,
      value: record.value,
      date: record.period
    })) || [];



    return {
      ...currentKpiData,
      chartData
    };
  }, [currentKpiData, historyData]);

  // Listen for drawer filter changes
  useEffect(() => {
    const handleDrawerFiltersChanged = (event: any) => {
      if (event.kpiId === currentKPI || !event.kpiId) {
        setDrawerFilters(event.filters);
      }
    };

    kpiEvents.on('drawer:filters-changed', handleDrawerFiltersChanged);

    return () => {
      kpiEvents.off('drawer:filters-changed', handleDrawerFiltersChanged);
    };
  }, [currentKPI]);

  // Sync with dashboard filters when drawer opens
  useEffect(() => {
    if (isOpen) {
      setDrawerFilters(dashboardFilters);
    }
  }, [isOpen, dashboardFilters]);

  // Controlar scroll do body quando drawer está aberto
  useEffect(() => {
    if (isOpen) {
      // Desabilitar scroll do body
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = '0px'; // Evitar shift de layout
    } else {
      // Reabilitar scroll do body
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }

    // Cleanup ao desmontar o componente
    return () => {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    };
  }, [isOpen]);

  // Fechar com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeDrawer();
      }
    };
    
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [isOpen, closeDrawer]);

  // Set selected card data when drawer opens (no DOM manipulation needed)
  useEffect(() => {
    if (isOpen && currentKPI) {
      // Just set the selected card data without DOM manipulation
      setSelectedCardData({
        kpiId: currentKPI
      });

      return () => {
        setSelectedCardData(null);
      };
    }
  }, [isOpen, currentKPI]);

  return (
    <AnimatePresence>
      {isOpen && currentKPI && (
        <>
          {/* Background Blur Overlay */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            data-drawer-open="true"
            onClick={closeDrawer}
          />
          
          {/* Split Screen Layout */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="fixed inset-0 z-50 flex"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Left Side - Selected Card */}
            <motion.div 
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="w-[45%] flex items-center justify-center p-8"
            >
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="w-full max-w-md"
              >
                {kpiWithChartData && (
                  <div className="transform transition-all duration-300 hover:scale-105">
                    <KpiBentoCard
                      kpi={kpiWithChartData}
                      filters={drawerFilters}
                      isLarge={false}
                    />
                  </div>
                )}
              </motion.div>
            </motion.div>
            
            {/* Right Side - Drawer Content com scroll isolado */}
            <motion.div 
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="w-[55%] bg-white shadow-2xl overflow-y-auto max-h-screen"
            >
              <KPIDrawerContent
                kpiId={currentKPI}
                onClose={closeDrawer}
                initialTimeframe={drawerFilters.timeframe as any}
                initialCurrency={drawerFilters.currency as any}
                userId="test_user_ceo"
                profileType={selectedProfile}
                syncWithDashboard={true}
                currentKpiData={currentKpiData}
              />
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};