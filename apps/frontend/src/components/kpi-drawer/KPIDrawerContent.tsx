import React, { useState, useCallback, useEffect } from 'react';
import { SuperAgentMode } from './SuperAgentMode';
import KPIHistoryTable from './KPIHistoryTable';
import KPIClientRankingTable from './KPIClientRankingTable';
import { ActionButtons } from './ActionButtons';
import { DrawerTimeframeSelector, TimeframeOption } from './DrawerTimeframeSelector';
import { useDrawerFiltersWithRefresh, CurrencyOption } from '@/hooks/useDrawerFilters';
import { useKpis } from '@/hooks/useKpis';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';
import { X, Send, Settings, BarChart3, Edit3, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface Props {
  kpiId: string;
  onClose: () => void;
  initialTimeframe?: TimeframeOption;
  initialCurrency?: CurrencyOption;
  clientId?: string;
  userId?: string;
  profileType?: string;
  syncWithDashboard?: boolean;
}

export const KPIDrawerContent: React.FC<Props> = ({
  kpiId,
  onClose,
  initialTimeframe = 'week',
  initialCurrency = 'all',
  clientId = 'default',
  userId = 'default_user',
  profileType,
  syncWithDashboard = true
}) => {
  // State management
  const [superAgentActive, setSuperAgentActive] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');
  const [message, setMessage] = useState('');
  const [isDataRefreshing, setIsDataRefreshing] = useState(false);

  // Get dashboard filters for KPI data
  const { filters: dashboardFilters } = useDashboardFilters();

  // Get KPI data with personalization
  const { kpis, isLoading: kpisLoading } = useKpis({
    filters: dashboardFilters,
    enablePersonalization: true,
    userId: userId,
    profileType: profileType as any
  });

  // Find the current KPI data
  const currentKpiData = kpis.find(kpi => kpi.id === kpiId);



  // Drawer filters with real-time updates
  const drawerFilters = useDrawerFiltersWithRefresh({
    initialTimeframe,
    initialCurrency,
    syncWithDashboard,
    emitEvents: true,
    kpiId,
    onFiltersChange: useCallback((filters) => {
      // Trigger data refresh when filters change
      setIsDataRefreshing(true);

      // Simulate data refresh completion
      setTimeout(() => {
        setIsDataRefreshing(false);
      }, 1000);
    }, [])
  });

  // TODO: Replace with real KPI data from useKpis hook
  const kpiData = {
    title: `KPI ${kpiId}`,
    value: '0',
    variation: '+0%',
    status: 'neutral' as const
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6">
        <button 
          onClick={onClose}
          className="float-right p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X className="w-4 h-4 text-gray-400" />
        </button>
        <h2 className="text-xl font-bold text-gray-900 pr-8">
          {kpiData.description}
        </h2>
        <p className="text-gray-600 mt-2">
          {kpiData.interpretation}
        </p>
      </div>

      {/* Navigation */}
      <div className="px-6 py-4 flex items-center gap-4">
        <button 
          className={cn(
            "px-3 py-2 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors",
            activeTab === 'settings' && "text-gray-700 border-b-2 border-blue-600"
          )}
          onClick={() => setActiveTab('settings')}
        >
          <Settings className="w-4 h-4" />
        </button>
        <button 
          className={cn(
            "px-3 py-2 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors",
            activeTab === 'charts' && "text-gray-700 border-b-2 border-blue-600"
          )}
          onClick={() => setActiveTab('charts')}
        >
          <BarChart3 className="w-4 h-4" />
        </button>
        <button 
          className={cn(
            "px-3 py-2 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors",
            activeTab === 'edit' && "text-gray-700 border-b-2 border-blue-600"
          )}
          onClick={() => setActiveTab('edit')}
        >
          <Edit3 className="w-4 h-4" />
        </button>
        <div className="ml-auto">
          <Button
            size="sm"
            onClick={() => setSuperAgentActive(!superAgentActive)}
            className={cn(
              "transition-all border-2",
              superAgentActive 
                ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white border-transparent hover:from-blue-600 hover:to-purple-700" 
                : "bg-white text-gray-700 border-blue-300 hover:border-purple-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50"
            )}
          >
            {superAgentActive ? 'Agent Mode Ativo' : 'Super Agent Mode'}
          </Button>
        </div>
      </div>

      {/* Content Area */}
      <ScrollArea className="flex-1">
        <div className="p-6">
          {superAgentActive && (
            <SuperAgentMode kpiId={kpiId} />
          )}

          <div className="space-y-6">
            {/* Timeframe Selector */}
            <DrawerTimeframeSelector
              value={drawerFilters.filters.timeframe}
              onChange={drawerFilters.setTimeframe}
              isLoading={drawerFilters.isChanging || isDataRefreshing}
              disabled={superAgentActive}
            />

            <Separator />

            {/* History Table with Real-time Updates */}
            <KPIHistoryTable
              kpiId={kpiId}
              timeframe={drawerFilters.filters.timeframe}
              currency={drawerFilters.filters.currency}
              clientId={clientId}
              userId={userId}
              profileType={profileType}
            />

            {/* Client Ranking Table - Only for ranking KPIs */}
            {kpiId === 'top_10_clientes_volume' && currentKpiData?.rankingData && (
              <>
                <Separator />
                <KPIClientRankingTable
                  kpiId={kpiId}
                  rankingData={currentKpiData.rankingData}
                  isLoading={kpisLoading || isDataRefreshing}
                />
              </>
            )}

            <ActionButtons kpiId={kpiId} />
          </div>
        </div>
      </ScrollArea>

      {/* Chat Input */}
      <div className="p-6">
        <form onSubmit={(e) => { e.preventDefault(); }} className="relative">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ex: 'Configure alerta para volume acima de R$ 3M'"
            className="w-full pr-12"
          />
          <Button 
            type="submit" 
            size="icon"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8"
          >
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </div>
    </div>
  );
};