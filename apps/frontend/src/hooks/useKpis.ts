/**
 * Unified KPIs Hook - SINGLE SOURCE OF TRUTH
 * 
 * Main hook for KPI data management. Consolidates previous:
 * - useKpiData (removed)
 * - useDynamicKpis (merged)  
 * - useKpiDataTransition (merged)
 * 
 * Provides consistent loading states, error handling, and data flow.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  KpiData,
  DashboardFilters,
  UseKpiDataReturn
} from '@/types/kpi';
import {
  LoadingState,
  ErrorState,
  KpiListResponse,
  DashboardLayoutConfig
} from '@/types/dashboard';
import { UserProfile, ProfileType } from '@/types/profile';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://backend-production-9857.up.railway.app';

interface UseDynamicKpisProps {
  filters: DashboardFilters;
  autoRefresh?: boolean;
  refreshInterval?: number;
  // Week 5: Profile-aware props (optional for backward compatibility)
  userProfile?: UserProfile | null;
  profileType?: ProfileType;
  enablePersonalization?: boolean;
  userId?: string;
}

interface UseDynamicKpisReturn extends LoadingState, ErrorState {
  kpis: KpiData[];
  layoutConfig: DashboardLayoutConfig | null;
  totalCount: number;
  lastUpdated: string | null;
  refreshKpis: () => Promise<void>;
  togglePriority: (kpiId: string) => void;
  removeKpi: (kpiId: string) => void;
  addKpi: (kpiId: string) => Promise<void>;
  getKpiById: (kpiId: string) => KpiData | undefined;
}

export const useKpis = ({
  filters,
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
  // Week 5: Profile-aware props
  userProfile,
  profileType,
  enablePersonalization = false,
  userId
}: UseDynamicKpisProps): UseDynamicKpisReturn => {

  // State management
  const [kpis, setKpis] = useState<KpiData[]>([]);
  const [layoutConfig, setLayoutConfig] = useState<DashboardLayoutConfig | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  
  // Loading states
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  // Computed loading state
  const isLoading = isInitialLoading || isFilterChanging || isRefreshing;
  const hasError = error !== null;

  // API call to fetch KPIs
  const fetchKpis = useCallback(async (isRefresh = false) => {
    // Prevent multiple simultaneous requests
    if (isLoading && !isRefresh) {
      console.log('⏳ Skipping fetch - already loading');
      return;
    }

    try {
      console.log('🔄 Fetching KPIs with filters:', filters);

      // Set appropriate loading state
      if (isRefresh) {
        setIsRefreshing(true);
      } else if (kpis.length === 0) {
        setIsInitialLoading(true);
      } else {
        setIsFilterChanging(true);
      }

      setError(null);

      // Build query parameters dynamically
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      // Add refresh flag if needed
      if (isRefresh) {
        queryParams.append('force_refresh', 'true');
      }

      // Week 5: Add personalization parameters
      if (enablePersonalization && userId) {
        queryParams.append('user_id', userId);

        if (userProfile?.profileType || profileType) {
          queryParams.append('profile_type', userProfile?.profileType || profileType!);
        }
      }

      // Week 5: Use personalized endpoint if personalization is enabled AND profile is available
      console.log('🔍 [PERSONALIZATION DEBUG]', {
        enablePersonalization,
        userId,
        userProfileType: userProfile?.profileType,
        profileType,
        condition: enablePersonalization && userId && (userProfile?.profileType || profileType)
      });

      // PRIORITY: Use personalized endpoint if conditions are met
      if (enablePersonalization && userId && (userProfile?.profileType || profileType)) {
        console.log('✅ [PERSONALIZATION] Using personalized endpoint');

        // Use personalized endpoint with POST method
        const personalizedUrl = `${API_BASE_URL}/api/personalized-kpis`;
        console.log('🔗 [PERSONALIZATION] URL:', personalizedUrl);
        const requestOptions: RequestInit = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: userId,
            profile_type: userProfile?.profileType || profileType,
            timeframe: filters.timeframe,
            currency: filters.currency
          })
        };

        const response = await fetch(`${API_BASE_URL}/api/personalized-kpis`, requestOptions);

        if (!response.ok) {
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        const data: any = await response.json();

        // Handle personalized API response format
        const rawKpis = data.kpis || [];

        // Fetch chart data for each personalized KPI
        const kpisWithChartData = await Promise.all(
          rawKpis.map(async (kpi: any) => {
            // For ranking KPIs like top_10_clientes_volume, use the chartData from the personalized API
            if (kpi.id === 'top_10_clientes_volume' && kpi.chartData && kpi.chartData.data) {
              // Use the client ranking data directly from the personalized API
              return {
                ...kpi,
                chartData: kpi.chartData.data,  // This contains the individual client data
                rankingData: kpi.rankingData    // This contains the full ranking data
              };
            }

            try {
              const historyUrl = `${API_BASE_URL}/api/v1/kpis/${kpi.id}/history?timeframe=${filters.timeframe}&currency=${filters.currency}`;
              const historyResponse = await fetch(historyUrl);

              if (historyResponse.ok) {
                const historyData = await historyResponse.json();

                // Convert history_data to chartData format
                const chartData = historyData.history_data?.map((point: any) => ({
                  name: point.period,
                  value: point.value,
                  date: point.period,
                  formatted_value: point.formatted_value
                })) || [];

                return { ...kpi, chartData };
              } else {
                return { ...kpi, chartData: null };
              }
            } catch (error) {
              return { ...kpi, chartData: null };
            }
          })
        );

        // Convert API format to KpiData format expected by components
        const fetchedKpis = kpisWithChartData.map((kpi: any) => ({
          id: kpi.id,
          title: kpi.name || kpi.title,
          name: kpi.name || kpi.title,
          description: kpi.description,
          currentValue: kpi.value || kpi.currentValue || 0,
          previousValue: kpi.previousValue,
          change: kpi.change,
          changePercent: kpi.changePercent,
          trend: kpi.trend || 'neutral',
          status: kpi.status || 'neutral',
          unit: kpi.unit || '',
          category: kpi.category || 'general',
          priority: kpi.priority || 'medium',
          isStarred: kpi.isStarred || false,
          lastUpdated: kpi.lastUpdated || new Date().toISOString(),
          metadata: kpi.metadata || {},
          chartType: kpi.chartType || 'line',
          chartData: (() => {
            // Handle different chartData formats from backend
            if (kpi.chartData) {
              // If chartData is an object with 'data' property (hybrid service format)
              if (typeof kpi.chartData === 'object' && kpi.chartData.data && Array.isArray(kpi.chartData.data)) {
                return kpi.chartData.data;
              }
              // If chartData is already an array (direct format)
              else if (Array.isArray(kpi.chartData)) {
                return kpi.chartData;
              }
            }

            // Fallback to single point
            return [
              { name: 'Atual', value: kpi.value || kpi.currentValue || 0, date: new Date().toISOString() }
            ];
          })(),
          formattedValue: kpi.formattedValue || kpi.value?.toString() || '0',
          target: kpi.target,
          targetStatus: kpi.targetStatus || 'on-track',
          // IMPORTANT: Preserve rankingData for ranking KPIs
          rankingData: kpi.rankingData
        }));

        setKpis(fetchedKpis);
        setTotalCount(data.total_kpis || fetchedKpis.length);
        setLastUpdated(new Date().toISOString());

        console.log(`✅ Loaded ${fetchedKpis.length} personalized KPIs for profile: ${userProfile?.profileType || profileType}`);
        return;
      }

      // Fallback: use the standard KPI endpoint
      console.log('📊 Using standard KPI endpoint (personalization disabled)');

      const params = new URLSearchParams({
        client_id: 'default',
        user_id: 'default_user',
        timeframe: filters.timeframe,
        currency: filters.currency
      });


      const response = await fetch(`${API_BASE_URL}/api/v1/kpis?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data: any = await response.json();
      console.log('✅ Standard KPIs data:', data);

      // Handle standard API response format and convert to expected structure
      const rawKpis = data.kpis || [];

      // Fetch chart data for each KPI
      const kpisWithChartData = await Promise.all(
        rawKpis.map(async (kpi: any) => {
          try {
            const historyUrl = `${API_BASE_URL}/api/v1/kpis/${kpi.id}/history?timeframe=${filters.timeframe}&currency=${filters.currency}`;
            const historyResponse = await fetch(historyUrl);

            if (historyResponse.ok) {
              const historyData = await historyResponse.json();

              // Convert history_data to chartData format
              const chartData = historyData.history_data?.map((point: any) => ({
                name: point.period,
                value: point.value,
                date: point.period,
                formatted_value: point.formatted_value
              })) || [];

              return { ...kpi, chartData };
            } else {
              return { ...kpi, chartData: null };
            }
          } catch (error) {
            return { ...kpi, chartData: null };
          }
        })
      );

      // Convert API format to KpiData format expected by components
      const fetchedKpis = kpisWithChartData.map((kpi: any) => ({
        id: kpi.id,
        title: kpi.name || kpi.title,
        name: kpi.name || kpi.title,
        description: kpi.description,
        currentValue: kpi.value || kpi.currentValue || 0,
        previousValue: kpi.previousValue,
        changePercent: kpi.variation ? parseFloat(kpi.variation.replace(/[+%]/g, '')) : null,
        trend: kpi.status === 'positive' ? 'up' : kpi.status === 'negative' ? 'down' : 'stable',
        format: 'number', // Default format
        format_type: 'number',
        unit: kpi.unit || '',
        chartType: 'line', // Default chart type
        chart_type: 'line',
        chartData: (() => {
          // Handle different chartData formats from backend
          if (kpi.chartData) {
            // If chartData is an object with 'data' property (hybrid service format)
            if (typeof kpi.chartData === 'object' && kpi.chartData.data && Array.isArray(kpi.chartData.data)) {
              return kpi.chartData.data;
            }
            // If chartData is already an array (direct format)
            else if (Array.isArray(kpi.chartData)) {
              return kpi.chartData;
            }
          }

          // Fallback to single point
          return [
            { name: 'Atual', value: kpi.value || kpi.currentValue || 0, date: new Date().toISOString() }
          ];
        })(),
        alert: kpi.alert,
        isPriority: kpi.isPriority || false,
        is_priority: kpi.is_priority || false,
        order: kpi.order || 999,
        display_order: kpi.display_order || 999,
        category: kpi.category || 'general',
        priority: kpi.priority || 'medium',
        frequency: kpi.frequency || 'realtime',
        lastUpdated: kpi.last_updated || new Date().toISOString(),
        calculatedAt: new Date().toISOString(),
        metadata: kpi.metadata || {}
      }));

      setKpis(fetchedKpis);
      setTotalCount(fetchedKpis.length);
      setLastUpdated(new Date().toISOString());

      console.log(`✅ Loaded and converted ${fetchedKpis.length} standard KPIs`);

    } catch (err) {
      console.error('❌ Error fetching KPIs:', err);
      console.error('❌ Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        type: typeof err,
        err
      });
      setError(err instanceof Error ? err.message : 'Failed to load KPIs');
      setKpis([]);
    } finally {
      setIsInitialLoading(false);
      setIsFilterChanging(false);
      setIsRefreshing(false);
    }
  }, [filters, enablePersonalization, userId, userProfile, profileType]);

  // Manual refresh function
  const refreshKpis = useCallback(async () => {
    await fetchKpis(true);
  }, [fetchKpis]);

  // Toggle KPI priority
  const togglePriority = useCallback((kpiId: string) => {
    setKpis(prevKpis =>
      prevKpis.map(kpi =>
        kpi.id === kpiId
          ? { 
              ...kpi, 
              is_priority: !kpi.is_priority,
              isPriority: !kpi.is_priority  // Ensure both fields are updated for compatibility
            }
          : kpi
      )
    );

    // TODO: Persist priority change to backend
    console.log(`🔄 Toggled priority for KPI: ${kpiId}`);
  }, []);

  // Remove KPI from dashboard
  const removeKpi = useCallback((kpiId: string) => {
    setKpis(prevKpis => prevKpis.filter(kpi => kpi.id !== kpiId));
    
    // TODO: Persist removal to backend
    console.log(`🗑️ Removed KPI: ${kpiId}`);
  }, []);

  // Add KPI to dashboard
  const addKpi = useCallback(async (kpiId: string) => {
    try {
      // TODO: Call API to add KPI
      console.log(`➕ Adding KPI: ${kpiId}`);
      
      // Refresh to get updated list
      await refreshKpis();
    } catch (err) {
      console.error('❌ Error adding KPI:', err);
      setError(err instanceof Error ? err.message : 'Failed to add KPI');
    }
  }, [refreshKpis]);

  // Get KPI by ID
  const getKpiById = useCallback((kpiId: string): KpiData | undefined => {
    return kpis.find(kpi => kpi.id === kpiId);
  }, [kpis]);

  // Effect: Load KPIs when filters change
  useEffect(() => {
    fetchKpis();
  }, [fetchKpis]);

  // Effect: Auto-refresh if enabled
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      if (!isLoading) {
        refreshKpis();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isLoading, refreshKpis]);

  // Effect: Log filter changes
  useEffect(() => {
    console.log('🔍 Filters changed:', filters);
  }, [filters]);

  return {
    // Data
    kpis,
    layoutConfig,
    totalCount,
    lastUpdated,

    // Loading states
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    isLoading,

    // Error state
    error,
    hasError,

    // Actions
    refreshKpis,
    togglePriority,
    removeKpi,
    addKpi,
    getKpiById
  };
};

// Helper functions
export const kpiHelpers = {
  /**
   * Sort KPIs by display order and priority
   */
  sortKpis: (kpis: KpiData[]): KpiData[] => {
    return [...kpis].sort((a, b) => {
      // Priority KPIs first (check both field variants for compatibility)
      const aPriority = a.is_priority || a.isPriority;
      const bPriority = b.is_priority || b.isPriority;
      
      if (aPriority && !bPriority) return -1;
      if (!aPriority && bPriority) return 1;
      
      // Then by display order (check both field variants)
      const aOrder = a.display_order || a.order || 0;
      const bOrder = b.display_order || b.order || 0;
      return aOrder - bOrder;
    });
  },

  /**
   * Filter KPIs by category
   */
  filterByCategory: (kpis: KpiData[], category: string): KpiData[] => {
    return kpis.filter(kpi => kpi.category === category);
  },

  /**
   * Get unique categories from KPIs
   */
  getCategories: (kpis: KpiData[]): string[] => {
    const categories = new Set(kpis.map(kpi => kpi.category));
    return Array.from(categories).sort();
  },

  /**
   * Get priority KPIs only
   */
  getPriorityKpis: (kpis: KpiData[]): KpiData[] => {
    return kpis.filter(kpi => kpi.is_priority);
  }
};

export default useKpis;
